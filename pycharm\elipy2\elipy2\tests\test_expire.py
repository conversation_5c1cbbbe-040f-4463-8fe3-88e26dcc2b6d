"""
test_expire.py
"""

import json
import os
import pytest
from mock import patch, MagicMock
import elipy2
import elipy2.bilbo
import elipy2.core
import elipy2.filer
from elipy2 import build_metadata, build_metadata_utils
from elipy2.expire import ExpireUtils

from elipy2.exceptions import ELIPYException


@patch("elipy2.telemetry.upload_metrics", MagicMock())
class TestExpire:  # pylint: disable=too-many-public-methods
    @staticmethod
    def list_of_one_build():
        return [
            elipy2.bilbo.Build().from_dict(
                {
                    "_id": "\\some\\where",
                    "_source": {
                        "created": "1",
                        "updated": "2018-10-29 more stuff to sort",
                    },
                }
            )
        ]

    @pytest.fixture()
    def fixture_builds(self):
        return [
            elipy2.bilbo.Build().from_dict(
                {
                    "_id": "\\\\filer.test\\builds\\dice\\",
                    "_source": {"created": "1", "type": "code"},
                }
            ),
            elipy2.bilbo.Build().from_dict(
                {
                    "_id": "\\\\filer.test\\builds\\dice\\",
                    "_source": {"created": "1", "updated": "2", "type": "drone"},
                }
            ),
            elipy2.bilbo.Build().from_dict(
                {
                    "_id": "\\\\filer.test\\builds\\dice\\",
                    "_source": {
                        "created": "1",
                        "updated": "2018-10-28",
                        "deleted": "5",
                        "type": "code",
                    },
                }
            ),
            elipy2.bilbo.Build().from_dict(
                {"_id": "\\\\filer.test\\builds\\dice\\", "_source": {}}
            ),
            elipy2.bilbo.Build().from_dict({"_id": "\\\\filer.test\\builds\\dice\\"}),
            elipy2.bilbo.Build().from_dict(
                {
                    "_id": "\\\\filer.test\\builds\\dice'\\zero",
                    "_source": {
                        "created": "1",
                        "updated": "2018-10-29 more stuff to sort",
                        "type": "code",
                    },
                }
            ),
            elipy2.bilbo.Build().from_dict(
                {
                    "_id": "\\\\filer.test\\builds\\dice\\one",
                    "_source": {
                        "created": "1",
                        "updated": "2021-10-26T14:10:45.764266",
                        "in_use_until": "2021-10-26T14:10:45.764266",
                        "type": "code",
                    },
                }
            ),
            elipy2.bilbo.Build().from_dict(
                {
                    "_id": "\\\\filer.test\\builds\\dice\\two",
                    "_source": {
                        "created": "1",
                        "updated": "2021-10-20T14:10:45.764266",
                        "in_use_until": "2021-10-20T14:10:45.764266",
                        "type": "code",
                    },
                }
            ),
        ]

    @pytest.fixture(autouse=True)
    def fixture_bilbo_response(self):
        with patch(
            "elipy2.bilbo._BilboElasticSearch.get_all",
            return_value=json.dumps(
                [
                    {
                        "_id": "\\\\not\\a\\real\\bilbo\\address\\1",
                        "_type": "build",
                        "_source": {
                            "changelist": "1234",
                            "deleted": "2018-03-19T17:44:56.321000",
                            "created": "2018-01-01T00:00:01.000000",
                        },
                    },
                    {
                        "_id": "\\\\not\\a\\real\\bilbo\\address\\2",
                        "_type": "build",
                        "_source": {
                            "changelist": "1234",
                            "created": "2018-01-01T00:00:02.000000",
                        },
                    },
                ]
            ),
        ):
            yield

    @pytest.fixture(autouse=True)
    def fixture_delete_folder_with_robocopy(self):
        with patch(
            "elipy2.core.delete_folder_with_robocopy",
            MagicMock(spec=elipy2.core.delete_folder_with_robocopy),
        ) as mock_delete_folder_with_robocopy:
            yield mock_delete_folder_with_robocopy

    @pytest.fixture(autouse=True)
    def mock_delete_folder(self):
        with patch(
            "elipy2.core.delete_folder",
            MagicMock(spec=elipy2.core.delete_folder),
        ) as mocked_function:
            yield mocked_function

    @pytest.fixture(autouse=True)
    def mock_delete_with_onefs_api(self):
        with patch(
            "elipy2.filer.FilerUtils.delete_with_onefs_api",
            MagicMock(spec=elipy2.filer.FilerUtils.delete_with_onefs_api),
        ) as mocked_function:
            yield mocked_function

    @pytest.fixture(autouse=True)
    def fixture_metadata_manager(self):
        with patch(
            "elipy2.build_metadata_utils.setup_metadata_manager",
            MagicMock(
                spec=build_metadata_utils.setup_metadata_manager,
                return_value=MagicMock(spec=build_metadata.BuildMetadataManager),
            ),
        ) as mock_manager:
            yield mock_manager.return_value

    @patch("elipy2.expire.ExpireUtils.get_builds_to_expire")
    def test_expire_dry_run(
        self,
        mock_get_builds_to_expire,
        mock_delete_with_onefs_api,
        fixture_delete_folder_with_robocopy,
        fixture_builds,
    ):
        mock_get_builds_to_expire.return_value = (fixture_builds, [])
        ExpireUtils().expire("//not/a/build", 1, dry_run=True)
        assert fixture_delete_folder_with_robocopy.call_count == 0
        assert mock_delete_with_onefs_api.call_count == 0

    @patch("elipy2.expire.ExpireUtils.get_builds_to_expire")
    def test_expire_onefs_api(
        self,
        mock_get_builds_to_expire,
        mock_delete_with_onefs_api,
        fixture_delete_folder_with_robocopy,
        fixture_builds,
    ):
        mock_get_builds_to_expire.return_value = (fixture_builds, [])
        ExpireUtils().expire("//not/a/build", 1, dry_run=False, use_onefs_api=True)
        assert mock_delete_with_onefs_api.call_count == 8
        assert fixture_delete_folder_with_robocopy.call_count == 0

    @patch("elipy2.expire.ExpireUtils.get_builds_to_expire")
    def test_expire(
        self,
        mock_get_builds_to_expire,
        mock_delete_with_onefs_api,
        fixture_delete_folder_with_robocopy,
        fixture_builds,
    ):
        mock_get_builds_to_expire.return_value = (fixture_builds, [])
        ExpireUtils().expire("//not/a/build", 0, dry_run=False)
        assert mock_delete_with_onefs_api.call_count == 0
        assert fixture_delete_folder_with_robocopy.call_count == 8

    @patch("elipy2.filer.FilerUtils.delete_with_onefs_api")
    @patch("elipy2.expire.LOGGER")
    @patch("elipy2.expire.ExpireUtils.get_builds_to_expire")
    def test_expire_logs_error_and_continues_deletion_on_use_onefs_api_exception(
        self,
        mock_get_builds_to_expire,
        mock_logger,
        mock_delete_with_onefs_api,
        fixture_builds,
    ):
        mock_get_builds_to_expire.return_value = (fixture_builds, [])
        mock_delete_with_onefs_api.side_effect = ELIPYException("Test exception")
        ExpireUtils().expire("//not/a/build", 0, dry_run=False, use_onefs_api=True)
        # Expected: 1 summary error + 8 individual build errors = 9 total
        assert mock_logger.error.call_count == len(fixture_builds) + 1

    @patch("elipy2.core.delete_folder_with_robocopy")
    @patch("elipy2.expire.LOGGER")
    @patch("elipy2.expire.ExpireUtils.get_builds_to_expire")
    def test_expire_logs_error_and_continues_deletion_on_delete_filer_folder_exception(
        self,
        mock_get_builds_to_expire,
        mock_logger,
        mock_delete_folder_with_robocopy,
        fixture_builds,
    ):
        mock_get_builds_to_expire.return_value = (fixture_builds, [])
        mock_delete_folder_with_robocopy.side_effect = ELIPYException("Test exception")
        ExpireUtils().expire("//not/a/build", 0, dry_run=False)
        # Expected: 1 summary error + 8 individual build errors = 9 total
        assert mock_logger.error.call_count == len(fixture_builds) + 1

    @patch("elipy2.expire.LOGGER", MagicMock())
    @patch("elipy2.filer.FilerUtils.delete_with_onefs_api")
    @patch("elipy2.expire.ExpireUtils.get_builds_to_expire")
    def test_expire_handles_exceptions_from_delete_with_onefs_api(
        self,
        mock_get_builds_to_expire,
        mock_delete_with_onefs_api,
        fixture_builds,
    ):
        mock_get_builds_to_expire.return_value = (fixture_builds, [])
        mock_delete_with_onefs_api.side_effect = [
            None,
            None,
            None,
            None,
            None,
            None,
            None,
            ELIPYException("Test exception"),
        ]
        ExpireUtils().expire(
            "//not/a/build", 0, dry_run=False, use_onefs_api=True
        )  # Shouldn't raise exception
        assert mock_delete_with_onefs_api.call_count == 8

    @patch("elipy2.expire.LOGGER", MagicMock())
    @patch("elipy2.core.delete_folder_with_robocopy")
    @patch("elipy2.expire.ExpireUtils.get_builds_to_expire")
    def test_expire_handles_exceptions_from_delete_filer_folder(
        self,
        mock_get_builds_to_expire,
        mock_delete_folder_with_robocopy,
        fixture_builds,
    ):
        mock_get_builds_to_expire.return_value = (fixture_builds, [])
        mock_delete_folder_with_robocopy.side_effect = [
            None,
            None,
            None,
            None,
            None,
            None,
            None,
            ELIPYException("Test exception"),
        ]
        ExpireUtils().expire("//not/a/build", 0, dry_run=False)  # Shouldn't raise exception
        assert mock_delete_folder_with_robocopy.call_count == 8

    @patch("elipy2.expire.ExpireUtils.get_builds_to_expire")
    def test_expire_response_run(
        self, mock_expire, fixture_delete_folder_with_robocopy, fixture_metadata_manager
    ):
        builds = TestExpire.list_of_one_build()
        mock_expire.return_value = (builds, [])

        # Mock get_builds_matching to return the build object so delete_build gets called
        fixture_metadata_manager.get_builds_matching.return_value = builds

        ExpireUtils().expire("//not/a/build", 1, dry_run=False)

        # The method should call delete_build with the build ID, not the build object
        fixture_metadata_manager.delete_build.assert_called_once_with(builds[0].id)
        fixture_delete_folder_with_robocopy.assert_called_once_with(builds[0])

    @patch("elipy2.expire.ExpireUtils.get_builds_to_expire")
    def test_expire_response_run_invalid(
        self, mock_expire, fixture_delete_folder_with_robocopy, fixture_metadata_manager
    ):
        builds_to_delete = [
            elipy2.bilbo.Build().from_dict(
                {
                    "_id": "\\?some\\where*",
                    "_source": {
                        "created": "1",
                        "updated": "2018-10-29 more stuff to sort",
                    },
                }
            )
        ]
        mock_expire.return_value = (builds_to_delete, [])
        ExpireUtils().expire("\\?some\\where*", 1, dry_run=False)
        assert fixture_delete_folder_with_robocopy.call_count == 0
        assert fixture_metadata_manager.delete_build.call_count == 0

    @patch("elipy2.expire.ExpireUtils.get_builds_to_expire")
    def test_expire_response_dry_run(
        self, mock_expire, fixture_delete_folder_with_robocopy, fixture_metadata_manager
    ):
        mock_expire.return_value = (TestExpire.list_of_one_build(), [])
        ExpireUtils().expire("//not/a/build", 1, dry_run=True)
        assert fixture_metadata_manager.delete_build.call_count == 0
        assert fixture_delete_folder_with_robocopy.call_count == 0

    def test_get_builds_to_expire(
        self,
        fixture_metadata_manager,
        fixture_builds,
    ):
        # Test that builds get properly processed through the new integrated logic
        fixture_metadata_manager.get_build_ids.return_value = ["one", "two", "three"]
        fixture_metadata_manager.get_builds_matching.return_value = fixture_builds[0:1]

        # Mock scanning and querying
        with patch("elipy2.expire.ExpireUtils._scan_disk_builds") as mock_scan, patch(
            "elipy2.expire.ExpireUtils._query_all_bilbo_indices_for_build"
        ) as mock_query:
            mock_scan.return_value = ["one", "two", "three"]
            mock_query.return_value = fixture_builds[0:1]

            result = ExpireUtils().get_builds_to_expire("\\\\not\\a\\real\\bilbo\\address", 5, 1)
            assert result == ([], [])

    @patch("elipy2.expire.ExpireUtils._query_all_bilbo_indices_for_build")
    @patch("elipy2.expire.ExpireUtils._scan_disk_builds")
    def test_get_builds_to_expire_multi(
        self,
        mock_scan_disk_builds,
        mock_query_bilbo_for_build,
    ):
        # Mock disk scanning to return three build paths
        mock_scan_disk_builds.return_value = [
            r"\\filer.test\builds\DICE\one",
            r"\\filer.test\builds\DICE\two",
            r"\\filer.test\builds\DICE\three",
        ]

        def mock_bilbo_query(build_path):
            if "one" in build_path:
                return [
                    elipy2.bilbo.Build().from_dict(
                        {"_id": build_path, "_source": {"created": "1", "type": "code"}}
                    )
                ]
            elif "two" in build_path:
                return [
                    elipy2.bilbo.Build().from_dict(
                        {"_id": build_path, "_source": {"created": "2", "type": "code"}}
                    )
                ]
            elif "three" in build_path:
                return [
                    elipy2.bilbo.Build().from_dict(
                        {"_id": build_path, "_source": {"created": "3", "type": "code"}}
                    )
                ]
            return []

        mock_query_bilbo_for_build.side_effect = (
            mock_bilbo_query  # Test with maxamount=1, should keep 1 build and delete 2
        )
        result = ExpireUtils().get_builds_to_expire("\\\\not\\a\\real\\bilbo\\address", 1, 1)
        builds_to_delete, orphaned_builds = result
        assert builds_to_delete == [
            r"\\filer.test\builds\DICE\one",
            r"\\filer.test\builds\DICE\two",
        ]
        assert orphaned_builds == []

    @patch("elipy2.core.delete_folder")
    @patch("elipy2.filer.FilerUtils.delete_with_onefs_api")
    @patch("elipy2.os.listdir")
    def test_keep_n_at_path_with_onfs_api(
        self, mock_listdir, mock_delete_with_onefs_api, mock_delete_folder
    ):
        test_directories = ["q", "w"]
        # Create test directories (exist_ok=True handles existing directories)
        for directory in test_directories:
            os.makedirs(os.path.join(os.getcwd(), directory), exist_ok=True)
        mock_listdir.return_value = test_directories
        mock_delete_folder.return_value = True
        try:
            ExpireUtils().keep_n_at_path(os.getcwd(), 0, dry_run=False, use_onefs_api=True)
        finally:
            for directory in test_directories:
                dir_path = os.path.join(os.getcwd(), directory)
                if os.path.exists(dir_path):
                    os.rmdir(dir_path)

        assert mock_delete_folder.call_count == 0
        assert mock_delete_with_onefs_api.call_count == 2

    @patch("elipy2.core.delete_folder")
    @patch("elipy2.filer.FilerUtils.delete_with_onefs_api")
    @patch("elipy2.os.listdir")
    def test_keep_n_at_path(self, mock_listdir, mock_delete_with_onefs_api, mock_delete_folder):
        test_directories = ["q", "w"]
        # Create test directories (exist_ok=True handles existing directories)
        for directory in test_directories:
            os.makedirs(os.path.join(os.getcwd(), directory), exist_ok=True)
        mock_listdir.return_value = test_directories
        mock_delete_folder.return_value = True
        try:
            ExpireUtils().keep_n_at_path(os.getcwd(), 0, dry_run=False, use_onefs_api=False)
        finally:
            for directory in test_directories:
                dir_path = os.path.join(os.getcwd(), directory)
                if os.path.exists(dir_path):
                    os.rmdir(dir_path)

        assert mock_delete_folder.call_count == 2
        assert mock_delete_with_onefs_api.call_count == 0

    @patch("elipy2.core.delete_folder")
    @patch("elipy2.filer.FilerUtils.delete_with_onefs_api")
    @patch("elipy2.os.listdir")
    def test_keep_n_at_path_keep_all(
        self, mock_listdir, mock_delete_with_onefs_api, mock_delete_folder
    ):
        test_directories = ["q", "w"]
        # Create test directories (exist_ok=True handles existing directories)
        for directory in test_directories:
            os.makedirs(os.path.join(os.getcwd(), directory), exist_ok=True)
        mock_listdir.return_value = test_directories
        try:
            ExpireUtils().keep_n_at_path(os.getcwd(), 2, dry_run=False, use_onefs_api=False)
        finally:
            for directory in test_directories:
                dir_path = os.path.join(os.getcwd(), directory)
                if os.path.exists(dir_path):
                    os.rmdir(dir_path)
        assert mock_delete_folder.call_count == 0
        assert mock_delete_with_onefs_api.call_count == 0
