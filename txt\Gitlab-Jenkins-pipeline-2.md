Follow the below step for iteration on testing on <PERSON> job
1. Investigate and fix the code, make sure black/pylint and unittest is green for relevant (git monitored)Files
2. Commit with your autogenerated message and push to the remote branch
3. Monitor the pipeline on Gitlab to make sure all jobs are green (the pipeline on gitlab will be https://gitlab.ea.com/dre-cobra/elipy/elipy2/-/commits/branch_name or https://gitlab.ea.com/dre-cobra/elipy/elipy-scripts/-/commits/branch_name). if you update both elipy2 and elipy-scripts, you will have 2 pipelines to monitor
4. If any job failed, fix the code and repeat from step 1
5. Once the pipeline is green, navigate to the step deploy-pre-release-to-af2-prod-repo and check the line which have something like:
Submitting /builds/dre-cobra/elipy/elipy-scripts/dist/dice_elipy_scripts-10.2a1.dev13979-py3-none-any.whl
and rememeber the version number (10.2a1.dev13979 in this case)
6. navigate to the Jenkins job (user will provide the job URL on the prompt) and navigate to job configuration
7. On the configuration search for the line simlar to 
C:\dev\ci\install-elipy.bat elipy_bct.yml "elipy-scripts-version" "elipy2-version"
and replace the version with the version number you remembered in step 5
8. Save the configuration and navigate to the job and click on "Rebuild Last"
9. Monitor the job to make sure it's green, if it failed, fix the code and repeat from step 1
10. Once the job is green, look through the log and make sure it aligned with the logic you implemented
11. If you see any issue, fix the code and repeat from step 1. If its good then update the job description to the version number you used in step 5

During all step do not stop your session so user need to ask you to continue. Keep waiting for both pipeline and job to be green before proceeding to the next step.