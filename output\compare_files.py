
import sys

def compare_files(file1_path, file2_path):
    with open(file1_path, 'r') as f1:
        lines1 = set(f1.read().splitlines())
    with open(file2_path, 'r') as f2:
        lines2 = set(f2.read().splitlines())

    only_in_file1 = lines1 - lines2
    only_in_file2 = lines2 - lines1

    print("Lines only in {}:".format(file1_path))
    for line in sorted(only_in_file1):
        print(line)

    print("\nLines only in {}:".format(file2_path))
    for line in sorted(only_in_file2):
        print(line)

if __name__ == "__main__":
    file1 = sys.argv[1]
    file2 = sys.argv[2]
    compare_files(file1, file2)
